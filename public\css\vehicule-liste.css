/* Styles généraux */
:root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --accent-color: #f72585;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --border-radius: 0.5rem;
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
}

/* Header et navigation */
.page-header {
    background: linear-gradient(120deg, #4361ee 0%, #7209b7 100%);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTI4MCAxNDAiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjEpIj48cGF0aCBkPSJNMTI4MCAwTDY0MCAxNDBMMCAweiIvPjwvZz48L3N2Zz4=');
    background-size: 100% 100%;
    opacity: 0.2;
    z-index: 0;
}

.page-header h4 {
    color: white;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    position: relative;
    z-index: 1;
}

.page-header p {
    color: rgba(255,255,255,0.9);
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    max-width: 700px;
    position: relative;
    z-index: 1;
}

.page-header .breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
    position: relative;
    z-index: 1;
}

.page-header .breadcrumb-item, 
.page-header .breadcrumb-item a {
    color: rgba(255,255,255,0.8);
    font-weight: 500;
    text-decoration: none;
}

.page-header .breadcrumb-item.active {
    color: rgba(255,255,255,1);
}

.page-header .breadcrumb-item+.breadcrumb-item::before {
    color: rgba(255,255,255,0.6);
}

.btn-add-vehicle {
    background: linear-gradient(45deg, #f72585, #b5179e);
    border: none;
    border-radius: 50px;
    padding: 0.75rem 1.5rem;
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 10px rgba(0,0,0,0.15);
    transition: var(--transition);
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.btn-add-vehicle:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.2);
    color: white;
}

.btn-add-vehicle::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #b5179e, #f72585);
    z-index: -1;
    transition: opacity 0.3s ease;
    opacity: 0;
}

.btn-add-vehicle:hover::after {
    opacity: 1;
}

.btn-add-vehicle i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

/* Cartes statistiques modernes - Design dashboard appliqué */
.dashboard-stats-container {
    padding: 2rem 0;
}

.stat-card {
    background: #ffffff;
    border-radius: 24px;
    border: 1px solid rgba(0,0,0,0.05);
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
    height: 280px;
    backdrop-filter: blur(10px);
}

/* Bordure latérale colorée */
.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 24px 0 0 24px;
    opacity: 1;
    transition: all 0.4s ease;
}

.stat-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.stat-card:hover::before {
    width: 12px;
    box-shadow: 0 0 20px rgba(0,0,0,0.2);
}

.stat-card-header {
    padding: 1.5rem 1.5rem 0 2rem;
    position: relative;
    z-index: 2;
}

.stat-card-icon {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.2rem;
    color: white;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
}

.stat-card-header.good .stat-card-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
}

.stat-card-header.warning .stat-card-icon {
    background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
    box-shadow: 0 8px 32px rgba(255, 167, 38, 0.4);
}

.stat-card-header.bad .stat-card-icon {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    box-shadow: 0 8px 32px rgba(255, 107, 107, 0.4);
}

.stat-card-body {
    padding: 2rem 2rem 1rem 2rem;
    height: calc(100% - 80px);
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 2;
}

.stat-card-value {
    font-size: 3rem;
    font-weight: 800;
    color: #1e293b;
    line-height: 1;
    margin-bottom: 0.5rem;
    animation: countUp 2s ease-out;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

@keyframes countUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.stat-card-subtitle {
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: #475569;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.stat-card-footer {
    margin-top: auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
}

.stat-card-footer span {
    font-size: 0.85rem;
    color: #64748b;
    font-weight: 500;
}

.stat-card-trend {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.6rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    border: none;
    position: relative;
    overflow: hidden;
}

.stat-card-trend.up {
    background: rgba(16, 185, 129, 0.1);
    color: #047857;
    border: 2px solid rgba(16, 185, 129, 0.2);
}

.stat-card-trend.up::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(5, 150, 105, 0.05));
    z-index: -1;
}

.stat-card-trend.down {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 2px solid rgba(239, 68, 68, 0.2);
}

.stat-card-trend.down::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.05), rgba(220, 38, 38, 0.05));
    z-index: -1;
}

/* Bordures colorées par type */
.stat-card:has(.stat-card-header.good) {
    border-left: 6px solid #667eea;
    border-right: 4px solid rgba(102, 126, 234, 0.3);
    border-top: 2px solid rgba(102, 126, 234, 0.2);
    border-bottom: 2px solid rgba(102, 126, 234, 0.2);
}

.stat-card:has(.stat-card-header.good)::before {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
}

.stat-card:has(.stat-card-header.warning) {
    border-left: 6px solid #ffa726;
    border-right: 4px solid rgba(255, 167, 38, 0.3);
    border-top: 2px solid rgba(255, 167, 38, 0.2);
    border-bottom: 2px solid rgba(255, 167, 38, 0.2);
}

.stat-card:has(.stat-card-header.warning)::before {
    background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
    box-shadow: 0 0 20px rgba(255, 167, 38, 0.4);
}

.stat-card:has(.stat-card-header.bad) {
    border-left: 6px solid #ff6b6b;
    border-right: 4px solid rgba(255, 107, 107, 0.3);
    border-top: 2px solid rgba(255, 107, 107, 0.2);
    border-bottom: 2px solid rgba(255, 107, 107, 0.2);
}

.stat-card:has(.stat-card-header.bad)::before {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.4);
}

/* Effets de survol spécialisés */
.stat-card:has(.stat-card-header.good):hover {
    box-shadow: 0 20px 60px rgba(102, 126, 234, 0.2), 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.stat-card:has(.stat-card-header.warning):hover {
    box-shadow: 0 20px 60px rgba(255, 167, 38, 0.2), 0 0 0 2px rgba(255, 167, 38, 0.1);
}

.stat-card:has(.stat-card-header.bad):hover {
    box-shadow: 0 20px 60px rgba(255, 107, 107, 0.2), 0 0 0 2px rgba(255, 107, 107, 0.1);
}

/* Animations d'entrée */
.animate-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease-out forwards;
}

.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Filtres et recherche */
.filters-card {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    background: white;
}

.filters-card-body {
    padding: 1.5rem;
}

.search-box {
    position: relative;
    margin-bottom: 1rem;
}

.search-box input {
    padding-left: 3rem;
    border-radius: 50px;
    height: 3rem;
    border: 1px solid var(--gray-300);
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    transition: var(--transition);
}

.search-box input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-500);
    font-size: 1.25rem;
}

.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.filter-btn i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.advanced-filters {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--gray-200);
}

.advanced-filters-toggle {
    cursor: pointer;
    color: var(--primary-color);
    font-weight: 500;
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.advanced-filters-toggle i {
    margin-right: 0.5rem;
    transition: var(--transition);
}

.advanced-filters-toggle.collapsed i {
    transform: rotate(-90deg);
}

/* Tableau des véhicules */
.vehicle-table-card {
    border-radius: 20px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.15);
    overflow: hidden;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    backdrop-filter: blur(10px);
}

.table-responsive {
    overflow-x: auto;
}

.table {
    margin-bottom: 0;
    background: transparent;
    border-radius: 20px;
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 700;
    border: none;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 1px;
    padding: 1.5rem 1rem;
    vertical-align: middle;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table tbody td {
    padding: 1.5rem 1rem;
    vertical-align: middle;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    color: var(--gray-800);
    font-size: 0.9rem;
    background: rgba(255,255,255,0.8);
    transition: all 0.3s ease;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

.table tbody tr {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    transform: scale(1.01);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.vehicle-img {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: var(--transition);
}

.vehicle-img:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0,0,0,0.15);
}

.vehicle-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.vehicle-status {
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
}

.vehicle-status i {
    margin-right: 0.35rem;
    font-size: 0.875rem;
}

.vehicle-status.good {
    background-color: rgba(76, 175, 80, 0.1);
    color: #2e7d32;
}

.vehicle-status.fair {
    background-color: rgba(255, 152, 0, 0.1);
    color: #e65100;
}

.vehicle-status.bad {
    background-color: rgba(244, 67, 54, 0.1);
    color: #b71c1c;
}

.btn-action {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    font-size: 1rem;
    margin: 0 0.25rem;
}

.btn-action:hover {
    transform: translateY(-3px);
}

.btn-action.view {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
}

.btn-action.view:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-action.edit {
    background-color: rgba(255, 152, 0, 0.1);
    color: #e65100;
}

.btn-action.edit:hover {
    background-color: #e65100;
    color: white;
}

.btn-action.delete {
    background-color: rgba(244, 67, 54, 0.1);
    color: #b71c1c;
}

.btn-action.delete:hover {
    background-color: #b71c1c;
    color: white;
}

/* Modal de détails */
.vehicle-modal .modal-content {
    border: none;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.vehicle-modal .modal-header {
    background: linear-gradient(120deg, #4361ee 0%, #3a0ca3 100%);
    color: white;
    border-bottom: none;
    padding: 1.5rem;
}

.vehicle-modal .modal-title {
    font-weight: 600;
    display: flex;
    align-items: center;
}

.vehicle-modal .modal-title i {
    margin-right: 0.5rem;
    font-size: 1.25rem;
}

.vehicle-modal .btn-close {
    color: white;
    opacity: 0.8;
}

.vehicle-modal .modal-body {
    padding: 1.5rem;
}

.vehicle-img-lg {
    height: 250px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.vehicle-img-lg img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.info-card {
    background-color: var(--gray-100);
    border-radius: var(--border-radius);
    padding: 1.25rem;
    height: 100%;
}

.info-card h6 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--gray-800);
    border-bottom: 2px solid var(--gray-300);
    padding-bottom: 0.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.info-label {
    font-weight: 500;
    color: var(--gray-600);
}

.info-value {
    font-weight: 600;
    color: var(--gray-800);
}

.vehicle-modal .modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--gray-200);
}

/* Pagination */
.pagination {
    margin-top: 1.5rem;
    justify-content: center;
}

.pagination .page-item .page-link {
    border: none;
    margin: 0 0.25rem;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-700);
    font-weight: 500;
    transition: var(--transition);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    color: white;
}

.pagination .page-item .page-link:hover {
    background-color: var(--gray-200);
    color: var(--gray-800);
}

.pagination .page-item.active .page-link:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Responsive */
@media (max-width: 992px) {
    .page-header {
        padding: 1.5rem;
    }
    
    .page-header h4 {
        font-size: 1.5rem;
    }
    
    .stat-card {
        height: 240px;
    }

    .stat-card-icon {
        width: 60px;
        height: 60px;
        font-size: 2rem;
        top: 1.2rem;
        right: 1.2rem;
    }

    .stat-card-value {
        font-size: 2.5rem;
    }

    .stat-card-body {
        padding: 1.5rem;
    }

    .stat-card-subtitle {
        font-size: 0.8rem;
        margin-bottom: 0.8rem;
    }
}

@media (max-width: 768px) {
    .filter-buttons {
        flex-wrap: nowrap;
        overflow-x: auto;
        padding-bottom: 0.5rem;
    }
    
    .filter-btn {
        white-space: nowrap;
    }
    
    .table thead th {
        font-size: 0.7rem;
        padding: 0.75rem 0.5rem;
    }
    
    .table tbody td {
        padding: 0.75rem 0.5rem;
    }
    
    .btn-action {
        width: 1.75rem;
        height: 1.75rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 1.25rem;
    }
    
    .page-header h4 {
        font-size: 1.25rem;
    }
    
    .btn-add-vehicle {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .stat-card {
        height: 220px;
    }

    .stat-card-icon {
        width: 50px;
        height: 50px;
        font-size: 1.8rem;
        top: 1rem;
        right: 1rem;
    }

    .stat-card-value {
        font-size: 2rem;
    }

    .stat-card-body {
        padding: 1.2rem;
    }

    .stat-card-subtitle {
        font-size: 0.75rem;
        margin-bottom: 0.6rem;
    }
    
    .vehicle-img {
        width: 50px;
        height: 50px;
    }
}

/* Vue en grille améliorée */
.grid-view {
    display: grid !important;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 2rem;
    padding: 2rem 0;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Forcer l'affichage quand la classe d-none est retirée */
.grid-view:not(.d-none) {
    display: grid !important;
}

/* Grille responsive pour différentes tailles d'écran */
@media (min-width: 1400px) {
    .grid-view {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .grid-view {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1rem;
        padding: 0.5rem 0;
    }
}

@media (max-width: 576px) {
    .grid-view {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .grid-sort-options {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .grid-sort-options .form-select {
        width: 100%;
    }

    .d-flex.justify-content-between.align-items-center {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .d-flex.align-items-center.gap-3 {
        flex-direction: column;
        gap: 1rem !important;
    }

    .view-switch {
        justify-content: center;
    }
}

.vehicle-card {
    border-radius: 20px;
    border: none;
    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    position: relative;
    height: fit-content;
    min-height: 480px;
    display: flex;
    flex-direction: column;
    backdrop-filter: blur(10px);
}

.vehicle-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 20px 60px rgba(67, 97, 238, 0.25);
}

.vehicle-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    border-radius: 20px 20px 0 0;
}

.vehicle-card:hover::before {
    opacity: 1;
}

.vehicle-card-img {
    height: 240px;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px 20px 0 0;
}

.vehicle-card-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.vehicle-card:hover .vehicle-card-img img {
    transform: scale(1.05);
}

.vehicle-card-thumbnail {
    border-radius: 0;
}

.vehicle-card-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.25);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(255,255,255,0.3);
    z-index: 3;
    animation: pulse 2s infinite;
}

.vehicle-card-status.good {
    background: linear-gradient(135deg, #00f5a0, #00d9f5);
    color: white;
    box-shadow: 0 4px 20px rgba(0, 245, 160, 0.4);
}

.vehicle-card-status.fair {
    background: linear-gradient(135deg, #ffa726, #ff7043);
    color: white;
    box-shadow: 0 4px 20px rgba(255, 167, 38, 0.4);
}

.vehicle-card-status.warning {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    box-shadow: 0 4px 20px rgba(255, 107, 107, 0.4);
}

.vehicle-card-status.bad {
    background: linear-gradient(135deg, #ff3838, #ff1744);
    color: white;
    box-shadow: 0 4px 20px rgba(255, 56, 56, 0.4);
}

.vehicle-card-status:not(.good):not(.fair):not(.warning):not(.bad) {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
    box-shadow: 0 4px 20px rgba(116, 185, 255, 0.4);
}

.vehicle-card-body {
    padding: 2rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
}

.vehicle-card-title {
    font-size: 1.4rem;
    font-weight: 800;
    margin-bottom: 0.75rem;
    color: #1a202c;
    letter-spacing: -0.025em;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.vehicle-card-subtitle {
    font-size: 0.9rem;
    color: #6b7280;
    margin-bottom: 1.25rem;
    font-weight: 500;
}

.vehicle-card-info {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 15px;
    border: 2px solid rgba(255,255,255,0.8);
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.06);
}

.vehicle-card-info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.vehicle-card-info-label {
    font-size: 0.7rem;
    color: #6b7280;
    margin-bottom: 0.4rem;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.vehicle-card-info-value {
    font-size: 0.85rem;
    font-weight: 700;
    color: #1f2937;
    line-height: 1.2;
}

.vehicle-card-footer {
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    border-radius: 0 0 20px 20px;
}

.vehicle-card-department {
    font-size: 0.85rem;
    color: white;
    font-weight: 700;
    padding: 0.5rem 1rem;
    background: rgba(255,255,255,0.2);
    border-radius: 20px;
    border: 2px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
    max-width: 60%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.vehicle-card-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
}

/* Amélioration des boutons d'action dans les cartes */
.vehicle-card-actions .btn-action {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.vehicle-card-actions .btn-action.view {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.vehicle-card-actions .btn-action.view:hover {
    background: linear-gradient(135deg, #764ba2, #667eea);
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
}

.vehicle-card-actions .btn-action.edit {
    background: linear-gradient(135deg, #00f5a0, #00d9f5);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 245, 160, 0.4);
}

.vehicle-card-actions .btn-action.edit:hover {
    background: linear-gradient(135deg, #00d9f5, #00f5a0);
    transform: scale(1.1) rotate(-5deg);
    box-shadow: 0 6px 25px rgba(0, 245, 160, 0.6);
}

.vehicle-card-actions .btn-action.delete {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.vehicle-card-actions .btn-action.delete:hover {
    background: linear-gradient(135deg, #ee5a24, #ff6b6b);
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 25px rgba(255, 107, 107, 0.6);
}

.vehicle-card-actions .btn-action i {
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.vehicle-card-actions .btn-action:hover i {
    transform: scale(1.2);
}

/* Animation de chargement pour les cartes */
@keyframes cardFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.vehicle-card {
    animation: cardFadeIn 0.5s ease-out;
}

.vehicle-card:nth-child(1) { animation-delay: 0.1s; }
.vehicle-card:nth-child(2) { animation-delay: 0.2s; }
.vehicle-card:nth-child(3) { animation-delay: 0.3s; }
.vehicle-card:nth-child(4) { animation-delay: 0.4s; }
.vehicle-card:nth-child(5) { animation-delay: 0.5s; }
.vehicle-card:nth-child(6) { animation-delay: 0.6s; }

/* Options de tri pour la grille */
.grid-sort-options {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.grid-sort-options .form-select {
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    background-color: white;
    transition: all 0.2s ease;
}

.grid-sort-options .form-select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.grid-sort-options .btn {
    border: 1px solid #d1d5db;
    background: white;
    color: #6b7280;
    transition: all 0.2s ease;
}

.grid-sort-options .btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
    color: #374151;
}

/* Carte de contrôles de vue */
.view-controls-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
    border: 1px solid var(--gray-200);
}

.view-info h6 {
    color: var(--gray-800);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.view-info small {
    color: var(--gray-600);
    font-size: 0.875rem;
}

/* Switch vue tableau/grille */
.view-switch {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.view-switch-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

.view-switch-buttons {
    display: flex;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid var(--gray-300);
}

.view-switch-btn {
    padding: 0.75rem 1rem;
    background-color: white;
    border: none;
    color: var(--gray-600);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    border-right: 1px solid var(--gray-300);
}

.view-switch-btn:last-child {
    border-right: none;
}

.view-switch-btn:hover {
    background-color: var(--gray-100);
    color: var(--gray-800);
}

.view-switch-btn.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.view-switch-btn.active:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    color: white;
}

.view-switch-btn i {
    font-size: 1rem;
}

.view-switch-btn span {
    font-weight: 600;
}

/* Loader */
.loader {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
}

.loader-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(67, 97, 238, 0.1);
    border-radius: 50%;
    border-top: 4px solid var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tooltip */
.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: 0.25rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.bs-tooltip-auto[x-placement^=top] .arrow::before,
.bs-tooltip-top .arrow::before {
    border-top-color: var(--dark-color);
}

/* Correction pour l'affichage dans le dashboard */
.page-content {
    min-height: calc(100vh - 200px) !important;
    overflow-x: hidden !important;
    position: relative !important;
    z-index: 1 !important;
    padding: 1.5rem !important;
}

.page-wrapper {
    overflow-x: hidden !important;
}

/* Assurer que le contenu ne déborde pas */
.container-fluid {
    max-width: 100% !important;
    overflow-x: hidden !important;
}

/* Boutons d'action personnalisés pour le tableau */
.btn-group-custom {
    display: flex;
    gap: 0.25rem;
}

.btn-action-custom {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    font-size: 0.875rem;
    border: none;
    text-decoration: none;
}

.btn-action-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-view-custom {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
}

.btn-view-custom:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-edit-custom {
    background-color: rgba(255, 152, 0, 0.1);
    color: #e65100;
}

.btn-edit-custom:hover {
    background-color: #e65100;
    color: white;
}

.btn-delete-custom {
    background-color: rgba(244, 67, 54, 0.1);
    color: #b71c1c;
}

.btn-delete-custom:hover {
    background-color: #b71c1c;
    color: white;
}

/* Miniature des véhicules dans le tableau */
.vehicle-thumbnail {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Pagination personnalisée */
.pagination-container {
    margin-top: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
}

.pagination-info {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

/* Utilisateur dans les cartes */
.vehicle-card-user {
    font-size: 0.85rem;
    color: rgba(255,255,255,0.9);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
}

.vehicle-card-user i {
    font-size: 1rem;
    color: rgba(255,255,255,0.8);
}

/* Animations */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.vehicle-card {
    animation: fadeInUp 0.6s ease-out;
}

.vehicle-card:nth-child(even) {
    animation: slideInRight 0.6s ease-out;
}

/* Assurer que les vues sont correctement cachées/affichées */
#tableView.d-none {
    display: none !important;
}

#gridView.d-none {
    display: none !important;
}

#tableView:not(.d-none) {
    display: block !important;
    animation: fadeInUp 0.5s ease-out;
}

#gridView:not(.d-none) {
    display: grid !important;
    animation: fadeInUp 0.5s ease-out;
}

/* Message aucun véhicule */
.no-vehicles-message {
    grid-column: 1 / -1;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
    margin: 2rem 0;
}

.no-vehicles-message .btn {
    margin-top: 1rem;
}
